image: google/cloud-sdk:alpine

stages:
  - lint
  - test
  - build
  - push
  - deploy

.auth_gcp: &auth_gcp
  before_script:
    - echo "$GCP_SERVICE_ACCOUNT_KEY" | base64 -d > /tmp/gcp-key.json
    - gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - gcloud config set project $GCP_PROJECT_ID
    - gcloud auth configure-docker ${GCP_REGION}-docker.pkg.dev
    - rm -f /tmp/gcp-key.json

.docker_setup: &docker_setup
  image: docker:25.0
  services:
    - docker:25.0-dind
  before_script:
    - apk add --no-cache docker-compose py3-pip

precommit:
  stage: lint
  when: manual
  allow_failure: true
  image: python:3.12
  variables:
    PRE_COMMIT_HOME: ${CI_PROJECT_DIR}/.cache/pre-commit
  cache:
    paths:
      - ${PRE_COMMIT_HOME}
  before_script:
    - pip install -q pre-commit
  script:
    - pre-commit clean
    - pre-commit run --show-diff-on-failure --color=always --all-files

pytest:
  <<: *docker_setup
  stage: test
  when: manual
  allow_failure: true
  script:
    - docker compose -f docker-compose.local.yml build
    - docker compose -f docker-compose.docs.yml build
    - docker compose -f docker-compose.local.yml run --rm django python manage.py migrate
    - docker compose -f docker-compose.local.yml up -d
    - docker compose -f docker-compose.local.yml run django pytest

build-image:
  <<: *docker_setup
  stage: build
  variables:
    IMAGE_TAG: ${CI_COMMIT_SHA}
  script:
    - docker compose -f docker-compose.gcp.yml build
    - docker save crate-pilot-wms:${CI_COMMIT_SHA} | gzip > crate-pilot-wms-${CI_COMMIT_SHA}.tar.gz
  artifacts:
    paths:
      - crate-pilot-wms-${CI_COMMIT_SHA}.tar.gz
    expire_in: 3 day

# New push job that loads and pushes the image
push-image:
  <<: *auth_gcp
  stage: push
  needs:
    - build-image
  when: manual
  allow_failure: true # Pipeline will succeed even if deployment isn't triggered
  script:
    # Install podman
    - apk add --no-cache podman
    # Configure podman with complete storage settings
    - mkdir -p /etc/containers
    - |
      cat > /etc/containers/storage.conf << EOF
      [storage]
      driver = "vfs"
      runroot = "/var/run/containers/storage"
      graphroot = "/var/lib/containers/storage"

      [storage.options]
      # Storage options to be specified

      [storage.options.vfs]
      # VFS-specific options
      EOF
    # Create required directories
    - mkdir -p /var/run/containers/storage /var/lib/containers/storage
    # Check podman info to verify configuration
    - podman info
    # Load the image from the compressed artifact
    - gzip -dc crate-pilot-wms-${CI_COMMIT_SHA}.tar.gz | podman load
    # Push the images to Google Artifact Registry
    - podman tag crate-pilot-wms:${CI_COMMIT_SHA} ${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${GCP_REPOSITORY}/crate-pilot-wms:latest
    - podman push ${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${GCP_REPOSITORY}/crate-pilot-wms:latest


# Add this after the push-image job and before deploy-sandbox
migrate-database:
  stage: deploy
  needs:
    - push-image
  when: manual
  allow_failure: true # Pipeline will succeed even if deployment isn't triggered
  rules:
    - if: '$GITLAB_USER_ID == "22171847" || $GITLAB_USER_ID == "23276115"'
      when: manual
    - when: never  # Block all other roles
  <<: *auth_gcp
  script:
    # Get django environment variables from the secret manager
    - gcloud secrets versions access latest --secret=crate_pilot_django_env > /tmp/.django

    # Create Cloud Run job for migrations (if not exists)
    - |
      if ! gcloud run jobs describe wms-migrate-database --region=${GCP_REGION} 2>/dev/null; then
        gcloud run jobs create wms-migrate-database \
          --image=${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${GCP_REPOSITORY}/crate-pilot-wms:latest \
          --region=${GCP_REGION} \
          --command="/bin/sh" \
          --args="-c,python manage.py migrate --noinput && python manage.py collectstatic --noinput" \
          --env-vars-file=/tmp/.django \
          --set-cloudsql-instances=${GCP_SQL_INSTANCE} \
          --set-secrets="DJANGO_SECRET_KEY=django-secret-key:latest" \
          --set-secrets="DATABASE_URL=database-url:latest" \
          --set-secrets="AWS_ACCESS_KEY_ID=AWS_ACCESS_KEY_ID:latest" \
          --set-secrets="AWS_SECRET_ACCESS_KEY=AWS_SECRET_ACCESS_KEY:latest" \
          --cpu=1 \
          --memory=512Mi \
          --task-timeout=10m
      else
        # Update existing job with new image
        gcloud run jobs update wms-migrate-database \
          --image=${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${GCP_REPOSITORY}/crate-pilot-wms:latest \
          --region=${GCP_REGION} \
          --env-vars-file=/tmp/.django \
          --set-cloudsql-instances=${GCP_SQL_INSTANCE} \
          --set-secrets="DJANGO_SECRET_KEY=django-secret-key:latest" \
          --set-secrets="DATABASE_URL=database-url:latest" \
          --set-secrets="AWS_ACCESS_KEY_ID=AWS_ACCESS_KEY_ID:latest" \
          --set-secrets="AWS_SECRET_ACCESS_KEY=AWS_SECRET_ACCESS_KEY:latest"
      fi

    # Execute the migration job and wait for completion
    - gcloud run jobs execute wms-migrate-database --region=${GCP_REGION} --wait

deploy-sandbox:
  stage: deploy
  when: manual
  allow_failure: true # Pipeline will succeed even if deployment isn't triggered
  rules:
    - if: '$GITLAB_USER_ID == "22171847" || $GITLAB_USER_ID == "23276115"'
      when: manual
    - when: never  # Block all other roles
  <<: *auth_gcp
  script:

    # Get django environment variables from the secret manager
    - gcloud secrets versions access latest --secret=crate_pilot_django_env > /tmp/.django

    # Deploy Django
    - gcloud run deploy wms-service
      --image=${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${GCP_REPOSITORY}/crate-pilot-wms:latest
      --region=${GCP_REGION}
      --platform=managed
      --allow-unauthenticated
      --min-instances=0
      --max-instances=2
      --cpu=1
      --memory=512Mi
      --env-vars-file=/tmp/.django
      --set-cloudsql-instances=${GCP_SQL_INSTANCE}
      --set-secrets="DJANGO_SECRET_KEY=django-secret-key:latest"
      --set-secrets="DATABASE_URL=database-url:latest"
      --set-secrets="AWS_ACCESS_KEY_ID=AWS_ACCESS_KEY_ID:latest"
      --set-secrets="AWS_SECRET_ACCESS_KEY=AWS_SECRET_ACCESS_KEY:latest"

    # Ensure Cloud Tasks queue exists
    - |
      if ! gcloud tasks queues describe queue-service --location=${GCP_REGION} 2>/dev/null; then
        gcloud tasks queues create queue-service \
          --location=${GCP_REGION} \
          --max-concurrent-dispatches=50 \
          --max-dispatches-per-second=200 \
          --max-attempts=5 \
          --min-backoff=5s
      fi

    - rm -f /tmp/.django


    # Set up Cloud Scheduler for periodic tasks (example)
#    - gcloud scheduler jobs create http process-edi-grn-mmm
#      --schedule="*/15 * * * *"
#      --uri="https://${DOMAIN_NAME}/api/edi/process-grn-mmm/"
#      --http-method=POST
#      --attempt-deadline=540s
#      --location=${GCP_REGION}
#      || true  # Don't fail if job already exists

