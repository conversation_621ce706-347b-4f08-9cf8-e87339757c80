from allauth.account.views import PasswordResetView
from django.contrib import messages
from django.contrib.auth.mixins import PermissionRequiredMixin, LoginRequiredMixin
from django.http import HttpResponseRedirect
from django.urls import reverse_lazy
from django.contrib.auth.models import Permission, Group
from django.contrib.messages.views import SuccessMessageMixin
from django.db.models import QuerySet, Prefetch
from django.shortcuts import redirect
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views import View
from django.views.generic import DetailView, UpdateView, ListView
import json

from django.contrib.auth import get_user_model

from wms.apps.users.tables import UserHTMxTable
from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView

from .forms import ConsignorFilterForm

User = get_user_model()


@method_decorator(require_http_methods(["GET", "POST"]), name='dispatch')
class IndexView(ExportTableMixin, CoreSingleTableView):
    model = User
    table_class = UserHTMxTable
    context_table_name = "table"
    template_name = "users/user_table_htmx.html"
    partial_template_name = "users/user_table_partial.html"
    search_fields = ['username', 'email']


class UserManageListView(PermissionRequiredMixin, ListView):
    model = User
    template_name = 'users/user_manage_list.html'
    context_object_name = 'users'
    permission_required = 'users.view_user'

    def get_queryset(self):
        return User.objects.prefetch_related(
            Prefetch('groups', queryset=Group.objects.only('name')),
            Prefetch('user_permissions', queryset=Permission.objects.only('name')),
        ).order_by('-date_joined')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        users_data = [{
            'id': user.pk,
            'email': user.email,
            'name': user.name,
            'date_joined': user.date_joined.isoformat(),
            'groups': [group.name for group in user.groups.all()],
            'permissions': [perm.name for perm in user.user_permissions.all()],
            'is_active': user.is_active,
        } for user in context['users']]  # 'users' is from context_object_name
        context['users'] = json.dumps(users_data)  # Pass as JSON string
        return context


class UserDetailView(DetailView):
    model = User
    slug_field = "id"
    slug_url_kwarg = "id"

user_detail_view = UserDetailView.as_view()


class UserUpdateView(SuccessMessageMixin, UpdateView):
    model = User
    fields = ["name"]
    success_message = _("Information successfully updated")

    def get_success_url(self) -> str:
        assert self.request.user.is_authenticated
        return self.request.user.get_absolute_url()

    def get_object(self, queryset: QuerySet | None = None) -> User:
        assert self.request.user.is_authenticated
        return self.request.user


user_update_view = UserUpdateView.as_view()

class SecurePasswordResetView(PasswordResetView):
    success_url = reverse_lazy('account_reset_password_done')

    def form_valid(self, form):
        """Override to prevent sending email if user doesn't exist"""
        # Get the email from the form
        email = form.cleaned_data["email"]

        # Check if user exists
        user_exists = User.objects.filter(email=email).exists()

        if not user_exists:
            # Don't send email, but show the same message
            messages.success(
                self.request,
                _("We have sent you an email if the address you provided is associated with an account. "
                  "Please check your inbox and follow the instructions to reset your password.")
            )
            return HttpResponseRedirect(self.success_url)

        # If user exists, proceed with normal password reset
        return super().form_valid(form)


class UpdateConsignorFilterView(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        form = ConsignorFilterForm(request.POST)
        if form.is_valid():
            request.user.consignor_filter = form.cleaned_data["consignor"]
            request.user.save(update_fields=["consignor_filter"])
        return redirect(request.META.get("HTTP_REFERER", "/"))
