from django import forms
from allauth.account.forms import SignupForm, LoginForm
from allauth.socialaccount.forms import SignupForm as SocialSignupForm
from django.contrib.auth import forms as admin_forms, get_user_model
from allauth.account.forms import AddEmailForm
from django.forms import Em<PERSON>Field
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import FormFieldSize
from wms.cores.forms.widget import CoreSelectWidget

from wms.apps.consignors.models import Consignor

User = get_user_model()


class UserAdminChangeForm(admin_forms.UserChangeForm):
    class Meta(admin_forms.UserChangeForm.Meta):  # type: ignore[name-defined]
        model = User
        field_classes = {"email": EmailField}


class UserAdminCreationForm(admin_forms.UserCreationForm):
    """
    Form for User Creation in the Admin Area.
    To change user signup, see UserSignupForm and UserSocialSignupForm.
    """

    class Meta(admin_forms.UserCreationForm.Meta):  # type: ignore[name-defined]
        model = User
        fields = ("email",)
        field_classes = {"email": Email<PERSON>ield}
        error_messages = {
            "email": {"unique": _("This email has already been taken.")},
        }


class UserSignupForm(SignupForm):
    """
    Form that will be rendered on a user sign up section/screen.
    Default fields will be added automatically.
    Check UserSocialSignupForm for accounts created from social.
    """


class UserSocialSignupForm(SocialSignupForm):
    """
    Renders the form when user has signed up using social accounts.
    Default fields will be added automatically.
    See UserSignupForm otherwise.
    """


class MyCustomAddEmailForm(AddEmailForm):

    def save(self, request):
        # Ensure you call the parent class's save.
        # .save() returns an allauth.account.models.EmailAddress object.
        email_address_obj = super(MyCustomAddEmailForm, self).save(request)

        # Add your own processing here.

        # You must return the original result.
        return email_address_obj


class ConsignorFilterForm(forms.Form):
    consignor = forms.ModelChoiceField(
        queryset=Consignor.objects.all(),
        required=False,
        label="Consignor",
        widget=CoreSelectWidget(
            attrs={
                "onchange": "this.form.submit();",
            },
            size=FormFieldSize.FULL
        ),
    )
