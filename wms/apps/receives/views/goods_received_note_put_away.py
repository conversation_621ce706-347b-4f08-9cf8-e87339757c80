from django.contrib import messages
from django.db import transaction
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponse
import json

from crispy_forms.helper import FormHelper

from wms.cores.views import CoreCreateView
from wms.apps.receives.forms.goods_received_note_put_away import (
    GoodsReceivedNotePutAwayForm,
    PutAwayRackFormSet
)
from wms.apps.receives.models import GoodsReceivedNote, GoodsReceivedNoteStockIn, GoodsReceivedNoteItem
from wms.apps.rackings.models import RackStorage, RackTransaction
from wms.apps.inventories.models import Stock


class GoodsReceivedNoteItemPutAwayFormView(CoreCreateView):
    """
    Display and process the formset for putting away received goods to multiple rack locations.
    """
    model = RackTransaction  # We create RackTransaction objects during put-away
    form_class = GoodsReceivedNotePutAwayForm
    template_name = 'receives/partials/goods_received_note_item_put_away_form.html'
    success_url = 'receives:goods_received_notes:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the GRN item
        self.goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=self.kwargs['pk'])
        self.goods_received_note = self.goods_received_note_item.goods_received_note

        # Check if GRN is in completed status
        if self.goods_received_note.status != GoodsReceivedNote.Status.COMPLETED:
            if self.request.headers.get('HX-Request'):
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Put-away is only allowed for completed goods received notes.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Put-away is only allowed for completed goods received notes."))
            return HttpResponse(status=400, content="GRN not eligible for put-away action.")

        # Check if item has received quantity
        if self.goods_received_note_item.get_received_quantity <= 0:
            if self.request.headers.get('HX-Request'):
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "No received quantity found for this item.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("No received quantity found for this item."))
            return HttpResponse(status=400, content="No received quantity available for put-away.")

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['goods_received_note_item'] = self.goods_received_note_item
        # Remove 'instance' since this is not a ModelForm
        kwargs.pop('instance', None)
        return kwargs

    def get_rack_formset(self):
        """Creates and returns the PutAwayRack formset with a Crispy Helper for table layout."""
        # Create the helper for the formset
        helper = FormHelper()
        helper.form_tag = False
        helper.disable_csrf = True
        helper.template = 'tailwind/table_inline_formset_htmx.html'
        helper.formset_header_title = _("Rack Locations")

        if self.request.method == 'POST':
            formset_instance = PutAwayRackFormSet(
                self.request.POST,
                prefix='racks',
                form_kwargs={'warehouse': self.goods_received_note.deliver_to}
            )
        else:
            formset_instance = PutAwayRackFormSet(
                prefix='racks',
                form_kwargs={'warehouse': self.goods_received_note.deliver_to}
            )

        formset_instance.helper = helper
        return formset_instance

    def _calculate_available_quantity(self):
        """Calculate the available quantity to put away for this GRN item."""
        # Calculate total received quantity for this item
        total_received = sum(
            stock_in.approved_quantity for stock_in in
            GoodsReceivedNoteStockIn.objects.filter(
                goods_received_note_item=self.goods_received_note_item
            )
        )

        # Calculate already put away quantity for this item
        already_put_away = sum(
            rack_tx.quantity for rack_tx in
            RackTransaction.objects.filter(
                type=RackTransaction.Type.GRN,
                rackstorage__stock__item=self.goods_received_note_item.item,
                rackstorage__stock__batch_no=self.goods_received_note_item.batch_no,
                rackstorage__stock__expiry_date=self.goods_received_note_item.expiry_date,
                rackstorage__stock__warehouse=self.goods_received_note.deliver_to
            )
        )

        return total_received - already_put_away

    def _get_or_create_stock(self):
        """Get or create stock object for this GRN item."""
        stock, created = Stock.objects.get_or_create(
            item=self.goods_received_note_item.item,
            batch_no=self.goods_received_note_item.batch_no,
            expiry_date=self.goods_received_note_item.expiry_date,
            warehouse=self.goods_received_note.deliver_to
        )
        return stock

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['goods_received_note_item'] = self.goods_received_note_item
        context['goods_received_note'] = self.goods_received_note

        # Get or create stock for this item
        stock = self._get_or_create_stock()
        context['stock'] = stock

        # Calculate quantities using helper method
        available_to_put_away = self._calculate_available_quantity()

        # Calculate individual components for display
        total_received = sum(
            stock_in.approved_quantity for stock_in in
            GoodsReceivedNoteStockIn.objects.filter(
                goods_received_note_item=self.goods_received_note_item
            )
        )

        already_put_away = sum(
            rack_tx.quantity for rack_tx in
            RackTransaction.objects.filter(
                type=RackTransaction.Type.GRN,
                rackstorage__stock=stock
            )
        )

        context['total_received'] = total_received
        context['already_put_away'] = already_put_away
        context['available_to_put_away'] = available_to_put_away

        # Add rack formset to context
        context['rack_formset'] = self.get_rack_formset()

        return context

    @transaction.atomic
    def form_valid(self, form):
        # Get the rack formset and validate it
        rack_formset = self.get_rack_formset()

        if not rack_formset.is_valid():
            return self.form_invalid(form, rack_formset=rack_formset)

        try:
            quantity_per_rack = form.cleaned_data['quantity']
            remark = form.cleaned_data.get('remark', '')

            # Get selected racks (excluding deleted forms)
            selected_racks = []
            for rack_form in rack_formset:
                if rack_form.is_valid() and not rack_form.cleaned_data.get('DELETE', False):
                    rack = rack_form.cleaned_data.get('rack')
                    if rack:
                        selected_racks.append(rack)

            if not selected_racks:
                form.add_error(None, _("At least one rack location must be selected."))
                return self.form_invalid(form, rack_formset=rack_formset)

            # Calculate available quantity to put away
            available_to_put_away = self._calculate_available_quantity()

            # Calculate total quantity to be put away
            total_quantity_to_put_away = quantity_per_rack * len(selected_racks)

            # Validate against available quantity
            if total_quantity_to_put_away > available_to_put_away:
                error_message = _("Total quantity to put away (%(total)s = %(quantity)s × %(racks)s racks) exceeds available quantity (%(available)s).") % {
                    'total': total_quantity_to_put_away,
                    'quantity': quantity_per_rack,
                    'racks': len(selected_racks),
                    'available': available_to_put_away
                }
                form.add_error('quantity', error_message)
                return self.form_invalid(form, rack_formset=rack_formset)

            # Get or create stock for this item
            stock = self._get_or_create_stock()

            # Create RackTransaction for each selected rack
            for rack in selected_racks:
                # Get or create RackStorage for the rack and stock
                rack_storage, created = RackStorage.objects.get_or_create(
                    rack=rack,
                    stock=stock
                )

                # Create RackTransaction for put-away
                RackTransaction.objects.create(
                    type=RackTransaction.Type.GRN,
                    rackstorage=rack_storage,
                    quantity=quantity_per_rack,
                    remark=remark,
                    created_by=self.request.user
                )

            # Check if all received items are now put away
            self._update_put_away_status()

            rack_names = [rack.full_name for rack in selected_racks]
            success_message = _("Successfully put away %(quantity)s units to each of %(count)s racks: %(racks)s. Total: %(total)s units.") % {
                'quantity': quantity_per_rack,
                'count': len(selected_racks),
                'racks': ', '.join(rack_names),
                'total': total_quantity_to_put_away
            }

            # For HTMX requests, return success response with modal close and tab refresh
            if self.request.headers.get('HX-Request'):
                # Get the detail URL for the GRN
                detail_url = reverse('receives:goods_received_notes:detail', kwargs={'pk': self.goods_received_note.pk})

                # Set up HTMX triggers for success notification, modal close, and tab refresh
                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": success_message,
                        "type": "success"
                    },
                    "refreshTabContent": {
                        "url": detail_url
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }

                # Return an empty response with headers
                return HttpResponse('', headers=headers, status=200)
            else:
                messages.success(self.request, success_message)
                return HttpResponse(
                    status=200,
                    headers={
                        'HX-Trigger': 'refreshTabContent'
                    }
                )

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, _("Error during put-away: %(error)s") % {'error': str(e)})
            return self.form_invalid(form, rack_formset=rack_formset)

    def form_invalid(self, form, rack_formset=None):
        # If rack_formset is not provided, get it
        if rack_formset is None:
            rack_formset = self.get_rack_formset()

        # For HTMX requests, return the form with errors
        if self.request.headers.get('HX-Request'):
            context = self.get_context_data(form=form)
            context['rack_formset'] = rack_formset
            return self.render_to_response(context)

        # For regular form submissions, return the standard response
        context = self.get_context_data(form=form)
        context['rack_formset'] = rack_formset
        return render(self.request, self.template_name, context)

    def _update_put_away_status(self):
        """Update the is_put_away status of the GRN if all items are put away."""
        # Get all received stock items for this GRN
        received_stock_ins = GoodsReceivedNoteStockIn.objects.filter(
            goods_received_note_item__goods_received_note=self.goods_received_note
        )

        total_received = sum(stock_in.approved_quantity for stock_in in received_stock_ins)

        # Get all put away quantities for this GRN
        put_away_transactions = RackTransaction.objects.filter(
            type=RackTransaction.Type.GRN,
            rackstorage__stock__in=[
                Stock.objects.filter(
                    item=stock_in.goods_received_note_item.item,
                    batch_no=stock_in.goods_received_note_item.batch_no,
                    expiry_date=stock_in.goods_received_note_item.expiry_date,
                    warehouse=self.goods_received_note.deliver_to
                ).first()
                for stock_in in received_stock_ins
            ]
        )

        total_put_away = sum(tx.quantity for tx in put_away_transactions if tx.rackstorage.stock)

        # Update is_put_away status if all received items are put away
        if total_put_away >= total_received:
            self.goods_received_note.is_put_away = True
            self.goods_received_note.save(update_fields=['is_put_away'])






