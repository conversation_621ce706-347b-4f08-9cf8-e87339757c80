from django import forms
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django.forms import formset_factory

from wms.cores.forms.fields import CoreModelForm, CoreModelChoiceField
from wms.cores.forms.widget import CoreNumberWidget, CoreTextAreaWidget, CoreSelectWidget
from wms.cores.utils import format_decimal_values

from wms.apps.receives.models import GoodsReceivedNote, GoodsReceivedNoteStockIn
from wms.apps.rackings.models import Rack, RackStorage, RackTransaction
from wms.apps.inventories.models import Stock
from decimal import Decimal


class PutAwayRackForm(forms.Form):
    """Individual rack form for put-away formset."""

    rack = CoreModelChoiceField(
        queryset=None,  # Will be set in __init__
        required=True,
        empty_label=_("Select a rack location..."),
        label=_("Rack Location"),
        widget=CoreSelectWidget(attrs={
            "class": "django-select2 put-away-rack w-full",
        }),
        help_text=_("Select the rack location where the items will be stored.")
    )

    def __init__(self, *args, **kwargs):
        self.warehouse = kwargs.pop('warehouse', None)
        super().__init__(*args, **kwargs)

        if self.warehouse:
            # Get available racks for the warehouse (only leaf nodes - pallets)
            available_racks = Rack.objects.filter(
                warehouse=self.warehouse,
                rack_type=Rack.RackType.PALLET
            ).order_by('full_name')

            self.fields['rack'].queryset = available_racks
            self.fields['rack'].label_from_instance = lambda obj: obj.full_name


class GoodsReceivedNotePutAwayForm(forms.Form):
    """Main form for putting away received goods with dynamic rack selection."""

    # Quantity to put away to each selected rack
    quantity = forms.IntegerField(
        required=True,
        label=_("Quantity per Rack"),
        widget=CoreNumberWidget(attrs={
            "class": "w-full",
            "id": "id_quantity",
            "step": "1",
            "min": "1",
        }),
        help_text=_("Enter the quantity to put away to each selected rack location.")
    )

    # Remark field
    remark = forms.CharField(
        required=False,
        label=_("Remark"),
        widget=CoreTextAreaWidget(attrs={"rows": 3, "class": "w-full"}),
        help_text=_("Optional remarks for this put-away operation.")
    )

    def __init__(self, *args, **kwargs):
        self.goods_received_note_item = kwargs.pop('goods_received_note_item', None)
        super().__init__(*args, **kwargs)

        if self.goods_received_note_item:
            # Calculate available quantity to put away
            total_received = sum(
                stock_in.approved_quantity for stock_in in
                GoodsReceivedNoteStockIn.objects.filter(
                    goods_received_note_item=self.goods_received_note_item
                )
            )

            # Get or create stock for this item
            stock, created = Stock.objects.get_or_create(
                item=self.goods_received_note_item.item,
                batch_no=self.goods_received_note_item.batch_no,
                expiry_date=self.goods_received_note_item.expiry_date,
                warehouse=self.goods_received_note_item.goods_received_note.deliver_to
            )

            # Calculate already put away quantity
            already_put_away = sum(
                rack_tx.quantity for rack_tx in
                RackTransaction.objects.filter(
                    type=RackTransaction.Type.GRN,
                    rackstorage__stock=stock
                )
            )

            available_to_put_away = total_received - already_put_away

            if available_to_put_away > 0:
                formatted_available_qty = format_decimal_values(available_to_put_away)
                self.fields['quantity'].widget.attrs['max'] = str(available_to_put_away)

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')

        if quantity is None:
            raise forms.ValidationError(_("Quantity is required."))

        if quantity <= 0:
            raise forms.ValidationError(_("Quantity must be greater than 0."))

        return quantity

    def clean(self):
        cleaned_data = super().clean()
        return cleaned_data


# Create the formset
PutAwayRackFormSet = formset_factory(
    PutAwayRackForm,
    extra=1,
    validate_min=True,
    can_delete=True,
    max_num=10  # Reasonable limit for rack selections
)
