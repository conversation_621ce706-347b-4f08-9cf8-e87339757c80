{% load i18n %}
{% load crispy_forms_tags %}
{% load humanize %}

<form
  method="post"
  hx-post="{% url 'receives:goods_received_notes:item_put_away_form' goods_received_note_item.pk %}"
  hx-trigger="submit"
>
  {% csrf_token %}
  <div class="p-0">
    <!-- Item Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Item Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Item Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Code" %}</label>
            <p class="mt-1 text-sm text-gray-900 font-medium">{{ goods_received_note_item.item.code }}</p>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item Name" %}</label>
            <p class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.item.name }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Batch No" %}</label>
            <p class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.batch_no|default:"-" }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Expiry Date" %}</label>
            <p class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.expiry_date|date:"Y-m-d"|default:"-" }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "UOM" %}</label>
            <p class="mt-1 text-sm text-gray-900">{{ goods_received_note_item.item.uom.symbol }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Warehouse" %}</label>
            <p class="mt-1 text-sm text-gray-900">{{ goods_received_note.deliver_to.name }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quantity Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Quantity Information" %}</h4>
      </div>
      <div class="p-4">
        <div class="grid grid-cols-3 gap-3">
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Total Received" %}</label>
            <p class="mt-1 text-sm text-gray-900 font-medium text-green-600">{{ total_received|floatformat:0 }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Already Put Away" %}</label>
            <p class="mt-1 text-sm text-gray-900 font-medium text-blue-600">{{ already_put_away|floatformat:0 }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Available to Put Away" %}</label>
            <p class="mt-1 text-sm text-gray-900 font-medium text-orange-600">{{ available_to_put_away|floatformat:0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Put Away Form Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-2 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Put Away Details" %}</h4>
      </div>
      <div class="p-4 space-y-4">
        <!-- Quantity per Rack Field -->
        <div>
          {{ form.quantity|as_crispy_field }}
          <p class="mt-1 text-xs text-gray-500">
            {% trans "This quantity will be put away to EACH selected rack location." %}
          </p>
        </div>

        <!-- Rack Selection Formset -->
        <div>
          {% crispy rack_formset rack_formset.helper %}
          <p class="mt-2 text-xs text-gray-500">
            {% trans "Select multiple rack locations. The quantity above will be put away to each rack." %}
          </p>
        </div>

        <!-- Remark Field -->
        <div>
          {{ form.remark|as_crispy_field }}
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-theme-border-primary">
      <button type="button"
              class="px-4 py-2 text-sm font-medium text-theme-text-secondary bg-white border border-theme-border-primary rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-primary"
              @click="$dispatch('close-modal')">
        {% trans "Cancel" %}
      </button>
      <button type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-theme-primary border border-transparent rounded-md hover:bg-theme-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-primary">
        {% trans "Put Away" %}
      </button>
    </div>
  </div>
</form>


