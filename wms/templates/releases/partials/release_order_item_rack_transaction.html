{% load i18n %}
{% load custom_tags %}

<div id="release-order-item-rack-transaction-{{ record.pk }}" class="text-sm">
  {% with rack_transactions=record.racktransaction_set.all %}
    {% if rack_transactions %}
      {% for transaction in rack_transactions %}
        {% if transaction.is_reserved %}
          <div class="mb-1 last:mb-0">
            <span class="font-medium">{{ transaction.rackstorage.rack.full_name }}</span>
            <span class="text-theme-text-secondary ml-1">({{ transaction.quantity|absolute|floatformat:2 }})</span>
          </div>
        {% endif %}
      {% endfor %}
    {% else %}
      <span class="text-theme-text-secondary">-</span>
    {% endif %}
  {% endwith %}
</div>
